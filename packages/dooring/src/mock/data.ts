/*
 * @Description: Mock data for dooring application
 * @Date: 2024-12-19
 */

// 推荐小程序数据
export const mockWechatList = [
  {
    id: "1",
    name: "智能客服小程序",
    title: "智能客服小程序",
    description: "基于AI的智能客服解决方案，提供24小时在线服务，支持多轮对话、智能回复、人工转接等功能",
    cover: "https://picsum.photos/300/200?random=1",
    thumbnail: "https://picsum.photos/300/200?random=1",
    author: "云搭科技",
    createTime: "2024-12-01",
    updateTime: "2024-12-15",
    viewCount: 1250,
    likeCount: 89,
    recommend: true,
    category: "商务",
    use: "other",
    tags: ["AI", "客服", "智能"],
    status: "published",
    type: "wechat",
    isHot: true,
    isNew: false,
    price: 0,
    originalPrice: 299,
    difficulty: "简单",
    duration: "30分钟"
  },
  {
    id: "2",
    name: "电商购物小程序",
    title: "电商购物小程序",
    description: "完整的电商购物解决方案，支持商品展示、购物车、支付、订单管理、会员系统等功能",
    cover: "https://picsum.photos/300/200?random=2",
    thumbnail: "https://picsum.photos/300/200?random=2",
    author: "电商团队",
    createTime: "2024-11-28",
    updateTime: "2024-12-10",
    viewCount: 2100,
    likeCount: 156,
    recommend: true,
    category: "电商",
    use: "mall",
    tags: ["购物", "电商", "支付"],
    status: "published",
    type: "wechat",
    isHot: true,
    isNew: false,
    price: 0,
    originalPrice: 599,
    difficulty: "中等",
    duration: "60分钟"
  },
  {
    id: "3",
    name: "餐厅点餐小程序",
    title: "餐厅点餐小程序",
    description: "便捷的餐厅点餐系统，支持在线点餐、桌位预约、扫码点餐、在线支付、外卖配送等功能",
    cover: "https://picsum.photos/300/200?random=3",
    thumbnail: "https://picsum.photos/300/200?random=3",
    author: "餐饮科技",
    createTime: "2024-11-25",
    updateTime: "2024-12-08",
    viewCount: 890,
    likeCount: 67,
    recommend: true,
    category: "餐饮",
    use: "food",
    tags: ["点餐", "餐厅", "预约"],
    status: "published",
    type: "wechat",
    isHot: false,
    isNew: true,
    price: 0,
    originalPrice: 399,
    difficulty: "简单",
    duration: "45分钟"
  },
  {
    id: "4",
    name: "健身打卡小程序",
    description: "健身爱好者的专属应用，记录运动数据，制定健身计划",
    cover: "https://picsum.photos/300/200?random=4", 
    author: "健身达人",
    createTime: "2024-11-20",
    updateTime: "2024-12-05",
    viewCount: 1580,
    likeCount: 123,
    recommend: true,
    category: "健康",
    tags: ["健身", "运动", "打卡"],
    status: "published"
  },
  {
    id: "5",
    name: "学习管理小程序",
    description: "在线学习平台，支持课程管理、作业提交、成绩查询",
    cover: "https://picsum.photos/300/200?random=5",
    author: "教育科技",
    createTime: "2024-11-15",
    updateTime: "2024-12-01",
    viewCount: 2350,
    likeCount: 198,
    recommend: true,
    category: "教育",
    tags: ["学习", "教育", "课程"],
    status: "published",
    type: "wechat",
    isHot: false,
    isNew: false,
    price: 0,
    originalPrice: 799,
    difficulty: "复杂",
    duration: "120分钟"
  },
  {
    id: "9",
    name: "在线教育小程序",
    title: "在线教育小程序",
    description: "专业的在线教育平台，支持视频课程、直播教学、作业提交、考试测评等功能",
    cover: "https://picsum.photos/300/200?random=9",
    thumbnail: "https://picsum.photos/300/200?random=9",
    author: "教育科技",
    createTime: "2024-10-25",
    updateTime: "2024-11-15",
    viewCount: 2890,
    likeCount: 234,
    recommend: true,
    category: "教育",
    use: "education",
    tags: ["教育", "在线", "课程"],
    status: "published",
    type: "wechat",
    isHot: false,
    isNew: true,
    price: 0,
    originalPrice: 799,
    difficulty: "复杂",
    duration: "120分钟"
  },
  {
    id: "10",
    name: "节日祝福小程序",
    title: "节日祝福小程序",
    description: "节日祝福卡片制作工具，支持多种节日模板、自定义祝福语、一键分享等功能",
    cover: "https://picsum.photos/300/200?random=10",
    thumbnail: "https://picsum.photos/300/200?random=10",
    author: "创意工作室",
    createTime: "2024-10-20",
    updateTime: "2024-11-10",
    viewCount: 1680,
    likeCount: 145,
    recommend: true,
    category: "节日",
    use: "festival",
    tags: ["节日", "祝福", "卡片"],
    status: "published",
    type: "wechat",
    isHot: false,
    isNew: false,
    price: 0,
    originalPrice: 199,
    difficulty: "简单",
    duration: "20分钟"
  },
  {
    id: "11",
    name: "游戏娱乐小程序",
    title: "游戏娱乐小程序",
    description: "休闲小游戏合集，包含多种经典小游戏，支持排行榜、好友对战等功能",
    cover: "https://picsum.photos/300/200?random=11",
    thumbnail: "https://picsum.photos/300/200?random=11",
    author: "游戏工作室",
    createTime: "2024-10-15",
    updateTime: "2024-11-05",
    viewCount: 3200,
    likeCount: 278,
    recommend: true,
    category: "娱乐",
    use: "recreation",
    tags: ["游戏", "娱乐", "休闲"],
    status: "published",
    type: "wechat",
    isHot: true,
    isNew: false,
    price: 0,
    originalPrice: 299,
    difficulty: "中等",
    duration: "45分钟"
  },
  {
    id: "12",
    name: "动漫资讯小程序",
    title: "动漫资讯小程序",
    description: "动漫爱好者的聚集地，提供最新动漫资讯、番剧推荐、同人作品分享等功能",
    cover: "https://picsum.photos/300/200?random=12",
    thumbnail: "https://picsum.photos/300/200?random=12",
    author: "动漫社区",
    createTime: "2024-10-10",
    updateTime: "2024-11-01",
    viewCount: 2450,
    likeCount: 189,
    recommend: true,
    category: "动漫",
    use: "cartoon",
    tags: ["动漫", "资讯", "社区"],
    status: "published",
    type: "wechat",
    isHot: false,
    isNew: true,
    price: 0,
    originalPrice: 399,
    difficulty: "中等",
    duration: "60分钟"
  }
];

// 推荐问卷数据
export const mockQuestionnaireList = [
  {
    id: "q1",
    title: "用户体验满意度调研",
    name: "用户体验满意度调研",
    description: "了解用户对产品的使用体验和满意度，收集用户反馈以改进产品功能",
    cover: "https://picsum.photos/300/200?random=11",
    thumbnail: "https://picsum.photos/300/200?random=11",
    author: "产品团队",
    createTime: "2024-12-01",
    updateTime: "2024-12-15",
    viewCount: 850,
    responseCount: 234,
    recommend: true,
    category: "用户调研",
    use: "other",
    tags: ["体验", "满意度", "调研"],
    status: "active",
    type: "questionnaire",
    isHot: true,
    isNew: false,
    price: 0,
    originalPrice: 199,
    difficulty: "简单",
    duration: "10分钟",
    questions: [
      {
        id: "q1_1",
        type: "single",
        title: "您对我们产品的整体满意度如何？",
        options: ["非常满意", "满意", "一般", "不满意", "非常不满意"]
      },
      {
        id: "q1_2", 
        type: "multiple",
        title: "您最喜欢我们产品的哪些功能？",
        options: ["界面设计", "功能完整性", "响应速度", "易用性", "客服支持"]
      }
    ]
  },
  {
    id: "q2",
    title: "市场需求分析问卷",
    description: "收集市场对新产品功能的需求和建议",
    cover: "https://picsum.photos/300/200?random=12",
    author: "市场部",
    createTime: "2024-11-28",
    updateTime: "2024-12-10",
    viewCount: 1200,
    responseCount: 456,
    recommend: true,
    category: "市场调研",
    tags: ["需求", "市场", "分析"],
    status: "active"
  },
  {
    id: "q3",
    title: "员工满意度调查",
    description: "了解员工对公司文化和工作环境的满意度",
    cover: "https://picsum.photos/300/200?random=13",
    author: "人事部",
    createTime: "2024-11-25",
    updateTime: "2024-12-08",
    viewCount: 680,
    responseCount: 89,
    recommend: true,
    category: "内部调研",
    tags: ["员工", "满意度", "文化"],
    status: "active"
  }
];

// 推荐图文数据
export const mockArticleList = [
  {
    id: "a1",
    title: "2024年小程序开发趋势分析",
    description: "深度解析小程序开发的最新趋势和技术发展方向",
    cover: "https://picsum.photos/300/200?random=21",
    author: "技术专家",
    createTime: "2024-12-01",
    updateTime: "2024-12-15",
    viewCount: 3200,
    likeCount: 245,
    recommend: true,
    category: "技术分析",
    tags: ["小程序", "开发", "趋势"],
    status: "published",
    content: "随着移动互联网的快速发展，小程序已经成为企业数字化转型的重要工具..."
  },
  {
    id: "a2",
    title: "用户体验设计的核心原则",
    description: "分享用户体验设计中的关键原则和最佳实践",
    cover: "https://picsum.photos/300/200?random=22",
    author: "设计师",
    createTime: "2024-11-28",
    updateTime: "2024-12-10",
    viewCount: 2800,
    likeCount: 189,
    recommend: true,
    category: "设计",
    tags: ["UX", "设计", "用户体验"],
    status: "published"
  },
  {
    id: "a3",
    title: "数据可视化最佳实践指南",
    description: "如何通过数据可视化提升业务洞察力",
    cover: "https://picsum.photos/300/200?random=23",
    author: "数据分析师",
    createTime: "2024-11-25",
    updateTime: "2024-12-08",
    viewCount: 1950,
    likeCount: 134,
    recommend: true,
    category: "数据分析",
    tags: ["数据", "可视化", "分析"],
    status: "published"
  },
  {
    id: "a4",
    title: "云原生架构设计模式",
    description: "探讨云原生环境下的架构设计模式和实践经验",
    cover: "https://picsum.photos/300/200?random=24",
    author: "架构师",
    createTime: "2024-11-20",
    updateTime: "2024-12-05",
    viewCount: 2650,
    likeCount: 201,
    recommend: true,
    category: "架构",
    tags: ["云原生", "架构", "设计模式"],
    status: "published"
  }
];

// API响应格式
export const createApiResponse = (data: any, message = "success") => {
  return {
    code: "00000",
    message,
    data: {
      list: data,
      total: data.length,
      pageNo: 1,
      pageSize: 10
    }
  };
};
