<!--
 * @Description: What's this for
 * @Autor: WangYuan1
 * @Date: 2024-10-30 14:58:09
 * @LastEditors: <PERSON><PERSON>uan
 * @LastEditTime: 2024-11-01 11:24:12
-->
<template>
  <div class="page">
    <!-- 页面头部 -->
    <div class="page-header mb-32px">
      <h1 class="c-#222529 font-extrabold text-24px mb-16px">为你推荐</h1>
      <p class="c-#666 text-14px">精选优质模板和内容，助力您的创作</p>
      <div class="stats-info mt-16px">
        <span class="stat-item">
          <span class="stat-number">{{ totalCount }}</span>
          <span class="stat-label">个推荐内容</span>
        </span>
        <span class="stat-item ml-24px">
          <span class="stat-number">{{ todayUpdate }}</span>
          <span class="stat-label">今日更新</span>
        </span>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section mb-32px">
      <div class="flex items-center gap-16px mb-16px">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索推荐内容..."
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="selectedCategory" placeholder="选择分类" style="width: 150px" @change="handleCategoryChange">
          <el-option label="全部" value="" />
          <el-option label="小程序" value="wechat" />
          <el-option label="问卷" value="questionnaire" />
          <el-option label="图文" value="article" />
        </el-select>
        <el-select v-model="selectedUse" placeholder="选择用途" style="width: 150px" @change="handleUseChange">
          <el-option label="全部用途" value="" />
          <el-option label="教育" value="education" />
          <el-option label="美食" value="food" />
          <el-option label="旅游" value="travel" />
          <el-option label="节日" value="festival" />
          <el-option label="娱乐" value="recreation" />
          <el-option label="动漫" value="cartoon" />
          <el-option label="电商" value="mall" />
          <el-option label="其他" value="other" />
        </el-select>
        <el-select v-model="sortBy" placeholder="排序方式" style="width: 150px" @change="handleSortChange">
          <el-option label="推荐排序" value="recommend" />
          <el-option label="最新发布" value="createTime" />
          <el-option label="最多浏览" value="viewCount" />
          <el-option label="最多点赞" value="likeCount" />
        </el-select>
      </div>
      <div class="filter-tags">
        <el-tag
          v-for="tag in popularTags"
          :key="tag"
          class="tag-item"
          :type="selectedTags.includes(tag) ? 'primary' : ''"
          @click="toggleTag(tag)"
        >
          {{ tag }}
        </el-tag>
      </div>
    </div>

    <!-- 推荐小程序 -->
    <div v-if="!selectedCategory || selectedCategory === 'wechat'" class="section">
      <div class="section-header">
        <h2 class="c-#222529 font-extrabold text-20px mb-24px">推荐小程序</h2>
        <span class="c-#999 text-12px">{{ filteredWechatList.length }} 个模板</span>
      </div>
      <div class="flex flex-wrap gap-30 mb-50">
        <ProductItem
          v-for="(item, index) in filteredWechatList"
          :key="index"
          :item="item"
          type="wechat"
        />
      </div>
      <div v-if="filteredWechatList.length === 0" class="empty-state">
        <p class="c-#999 text-center">暂无相关小程序模板</p>
      </div>
    </div>

    <!-- 推荐问卷 -->
    <div v-if="!selectedCategory || selectedCategory === 'questionnaire'" class="section">
      <div class="section-header">
        <h2 class="c-#222529 font-extrabold text-20px mb-24px">推荐问卷</h2>
        <span class="c-#999 text-12px">{{ filteredQuestionnaireList.length }} 个模板</span>
      </div>
      <div class="flex flex-wrap gap-30 mb-50">
        <ProductItem
          v-for="(item, index) in filteredQuestionnaireList"
          :key="index"
          :item="item"
          type="questionnaire"
        />
      </div>
      <div v-if="filteredQuestionnaireList.length === 0" class="empty-state">
        <p class="c-#999 text-center">暂无相关问卷模板</p>
      </div>
    </div>

    <!-- 推荐图文 -->
    <div v-if="!selectedCategory || selectedCategory === 'article'" class="section">
      <div class="section-header">
        <h2 class="c-#222529 font-extrabold text-20px mb-24px">推荐图文</h2>
        <span class="c-#999 text-12px">{{ filteredArticleList.length }} 个模板</span>
      </div>
      <div class="flex flex-wrap gap-30 mb-50">
        <ProductItem
          v-for="(item, index) in filteredArticleList"
          :key="index"
          :item="item"
          type="article"
        />
      </div>
      <div v-if="filteredArticleList.length === 0" class="empty-state">
        <p class="c-#999 text-center">暂无相关图文模板</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { http } from "@/utils/http";
import { ref, toRefs, reactive, defineProps, computed, onMounted } from "vue";
import { Search } from '@element-plus/icons-vue';
import ProductForPage from "@/components/ProductForPage";
import ProductItem from "@/components/ProductItem";
import { mockWechatList, mockQuestionnaireList, mockArticleList, createApiResponse } from "@/mock/data";

let wechatList = ref([]);
let questionnaireList = ref([]);
let articleList = ref([]);

// 搜索和筛选状态
const searchKeyword = ref('');
const selectedCategory = ref('');
const selectedUse = ref('');
const sortBy = ref('recommend');
const selectedTags = ref([]);

// 热门标签
const popularTags = ref(['AI', '电商', '教育', '旅游', '美食', '娱乐', '健康', '社交']);

// 统计信息
const totalCount = computed(() => {
  return wechatList.value.length + questionnaireList.value.length + articleList.value.length;
});

const todayUpdate = computed(() => {
  const today = new Date().toISOString().split('T')[0];
  const allItems = [...wechatList.value, ...questionnaireList.value, ...articleList.value];
  return allItems.filter(item => item.updateTime?.startsWith(today.replace(/-/g, '-'))).length;
});

// 计算属性：过滤后的列表
const filteredWechatList = computed(() => {
  return filterAndSortItems(wechatList.value);
});

const filteredQuestionnaireList = computed(() => {
  return filterAndSortItems(questionnaireList.value);
});

const filteredArticleList = computed(() => {
  return filterAndSortItems(articleList.value);
});

// 过滤和排序函数
function filterAndSortItems(items: any[]) {
  let filtered = items;

  // 关键词搜索
  if (searchKeyword.value) {
    filtered = filtered.filter(item =>
      item.name?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.title?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.author?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  }

  // 用途筛选
  if (selectedUse.value) {
    filtered = filtered.filter(item => item.use === selectedUse.value);
  }

  // 标签筛选
  if (selectedTags.value.length > 0) {
    filtered = filtered.filter(item =>
      selectedTags.value.some(tag => item.tags?.includes(tag))
    );
  }

  // 排序
  if (sortBy.value === 'createTime') {
    filtered = filtered.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
  } else if (sortBy.value === 'viewCount') {
    filtered = filtered.sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0));
  } else if (sortBy.value === 'likeCount') {
    filtered = filtered.sort((a, b) => (b.likeCount || 0) - (a.likeCount || 0));
  } else {
    // 推荐排序：热门 > 新品 > 浏览量
    filtered = filtered.sort((a, b) => {
      if (a.isHot !== b.isHot) return b.isHot ? 1 : -1;
      if (a.isNew !== b.isNew) return b.isNew ? 1 : -1;
      return (b.viewCount || 0) - (a.viewCount || 0);
    });
  }

  return filtered;
}

// 搜索处理
function handleSearch() {
  // 搜索逻辑已通过计算属性实现
}

// 分类筛选处理
function handleCategoryChange() {
  // 分类筛选逻辑已通过模板条件渲染实现
}

// 用途筛选处理
function handleUseChange() {
  // 用途筛选逻辑已通过计算属性实现
}

// 排序处理
function handleSortChange() {
  // 排序逻辑已通过计算属性实现
}

// 标签切换
function toggleTag(tag: string) {
  const index = selectedTags.value.indexOf(tag);
  if (index > -1) {
    selectedTags.value.splice(index, 1);
  } else {
    selectedTags.value.push(tag);
  }
}

getwWchatList();
getQuestionnaireList();
getArticleList();

function getwWchatList(page: any = { pageNo: 1, pageSize: 10 }) {
  // 直接使用mock数据，确保功能正常工作
  console.log("加载小程序推荐数据...");
  const filteredData = mockWechatList.filter(item => item.recommend);
  wechatList.value = filteredData;
}

function getQuestionnaireList(page: any = { pageNo: 1, pageSize: 10 }) {
  // 直接使用mock数据，确保功能正常工作
  console.log("加载问卷推荐数据...");
  const filteredData = mockQuestionnaireList.filter(item => item.recommend);
  questionnaireList.value = filteredData;
}

function getArticleList(page: any = { pageNo: 1, pageSize: 10 }) {
  // 直接使用mock数据，确保功能正常工作
  console.log("加载图文推荐数据...");
  const filteredData = mockArticleList.filter(item => item.recommend);
  articleList.value = filteredData;
}
</script>

<style lang="scss" scoped>
.page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: #fff;
  border-radius: 8px;
}

.page-header {
  text-align: center;
  padding: 30px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 32px;

  h1 {
    margin: 0 0 16px 0;
    color: #222529;
    font-weight: 800;
    font-size: 24px;
  }

  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.stats-info {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;

  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;

    .stat-number {
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
    }

    .stat-label {
      font-size: 12px;
      color: #999;
    }
  }
}

.filter-section {
  background: #fafbfc;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  margin-bottom: 32px;

  .flex {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .tag-item {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  }
}

.section {
  margin-bottom: 48px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;

  h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #222529;
    font-weight: 800;
    font-size: 20px;
  }

  span {
    color: #999;
    font-size: 12px;
  }
}

.empty-state {
  padding: 80px 20px;
  text-align: center;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;

  p {
    margin: 0;
    color: #999;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page {
    padding: 16px;
    margin: 0 16px;
  }

  .page-header {
    padding: 20px 0;

    h1 {
      font-size: 20px;
    }
  }

  .stats-info {
    flex-direction: column;
    gap: 12px;
  }

  .filter-section {
    padding: 16px;

    .flex {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .el-input,
      .el-select {
        width: 100% !important;
      }
    }
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    h2 {
      font-size: 18px;
    }
  }

  .empty-state {
    padding: 60px 16px;
  }
}

@media (max-width: 480px) {
  .page {
    margin: 0 8px;
    padding: 12px;
  }

  .page-header {
    padding: 16px 0;

    h1 {
      font-size: 18px;
    }
  }

  .filter-section {
    padding: 12px;
  }

  .section {
    margin-bottom: 32px;
  }
}
</style>
